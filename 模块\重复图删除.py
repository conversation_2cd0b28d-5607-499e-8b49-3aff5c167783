#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
🔍 重复图删除模块 🔍
功能: 使用最高效的方法检测和删除重复图片，支持多种对比算法
"""

import os
import logging
import hashlib
from collections import defaultdict
from PIL import Image
try:
    import imagehash
    IMAGEHASH_AVAILABLE = True
except ImportError:
    IMAGEHASH_AVAILABLE = False
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QFileDialog, QProgressBar, QMessageBox,
                            QCheckBox, QComboBox, QSpinBox,
                            QGroupBox, QTextEdit, QLineEdit)
from PyQt5.QtCore import QThread, pyqtSignal, QMutex

class DuplicateDetectionThread(QThread):
    """重复图片检测线程"""
    progress_signal = pyqtSignal(int, int)  # 当前进度, 总数
    status_signal = pyqtSignal(str)
    duplicate_found_signal = pyqtSignal(list)  # 发现的重复组
    complete_signal = pyqtSignal(bool, str, int, int)  # 成功, 消息, 总文件数, 重复文件数

    def __init__(self, folder_path, comparison_method="hash", similarity_threshold=95,
                 history_folder=None, comparison_mode="internal"):
        super().__init__()
        self.folder_path = folder_path
        self.comparison_method = comparison_method
        self.similarity_threshold = similarity_threshold
        self.history_folder = history_folder  # 历史图片文件夹
        self.comparison_mode = comparison_mode  # "internal"内部去重 或 "compare"对比去重
        self.is_running = True
        self.mutex = QMutex()
        
    def stop(self):
        """停止线程"""
        self.mutex.lock()
        self.is_running = False
        self.mutex.unlock()
        
    def get_file_hash(self, filepath):
        """获取文件MD5哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(filepath, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return None
    
    def get_image_hash(self, filepath, hash_type="phash"):
        """获取图片感知哈希值"""
        if not IMAGEHASH_AVAILABLE:
            # 如果imagehash不可用，返回文件哈希作为替代
            return self.get_file_hash(filepath)

        try:
            with Image.open(filepath) as img:
                if hash_type == "phash":
                    return str(imagehash.phash(img))
                elif hash_type == "dhash":
                    return str(imagehash.dhash(img))
                elif hash_type == "ahash":
                    return str(imagehash.average_hash(img))
                else:
                    return str(imagehash.phash(img))
        except Exception:
            return None

    def calculate_hash_similarity(self, hash1, hash2):
        """计算两个哈希值的相似度（百分比）"""
        if not IMAGEHASH_AVAILABLE:
            # 如果imagehash不可用，进行精确匹配
            return 100.0 if hash1 == hash2 else 0.0

        try:
            h1 = imagehash.hex_to_hash(hash1)
            h2 = imagehash.hex_to_hash(hash2)
            # 计算汉明距离
            hamming_distance = h1 - h2
            # 转换为相似度百分比（64位哈希）
            similarity = (64 - hamming_distance) / 64 * 100
            return similarity
        except Exception:
            return 0
    
    def run(self):
        try:
            if self.comparison_mode == "compare" and self.history_folder:
                self._run_compare_mode()
            else:
                self._run_internal_mode()
        except Exception as e:
            error_msg = f"检测过程中出错: {str(e)}"
            self.status_signal.emit(error_msg)
            self.complete_signal.emit(False, error_msg, 0, 0)

    def _run_internal_mode(self):
        """内部去重模式：在同一文件夹内查找重复图片"""
        self.status_signal.emit("正在扫描图片文件...")

        # 支持的图片格式
        img_exts = ('.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.tif', '.webp')

        # 获取所有图片文件
        image_files = []
        for filename in os.listdir(self.folder_path):
            if not self.is_running:
                return

            file_path = os.path.join(self.folder_path, filename)
            if os.path.isfile(file_path) and filename.lower().endswith(img_exts):
                # 获取文件大小进行预筛选
                try:
                    file_size = os.path.getsize(file_path)
                    image_files.append((filename, file_path, file_size))
                except Exception:
                    continue

        total_files = len(image_files)
        if total_files == 0:
            self.complete_signal.emit(False, "未找到图片文件", 0, 0)
            return

        self.status_signal.emit(f"找到 {total_files} 个图片文件，开始检测重复...")

        # 第一步：按文件大小分组（快速预筛选）
        size_groups = defaultdict(list)
        for filename, filepath, filesize in image_files:
            size_groups[filesize].append((filename, filepath))

        # 只处理有多个文件的大小组
        potential_duplicates = []
        for files in size_groups.values():
            if len(files) > 1:
                potential_duplicates.extend(files)

        if not potential_duplicates:
            self.complete_signal.emit(True, "未发现重复图片", total_files, 0)
            return

        self.status_signal.emit(f"预筛选出 {len(potential_duplicates)} 个可能重复的文件，开始详细对比...")

        # 第二步：计算哈希值
        file_hashes = {}
        processed = 0

        for filename, filepath in potential_duplicates:
            if not self.is_running:
                return

            if self.comparison_method == "md5":
                hash_value = self.get_file_hash(filepath)
            else:
                hash_value = self.get_image_hash(filepath, "phash")

            if hash_value:
                file_hashes[filepath] = {
                    'filename': filename,
                    'hash': hash_value,
                    'size': os.path.getsize(filepath)
                }

            processed += 1
            self.progress_signal.emit(processed, len(potential_duplicates))

            # 更新状态
            if processed % 10 == 0:
                self.status_signal.emit(f"已处理 {processed}/{len(potential_duplicates)} 个文件...")

        # 第三步：查找重复
        duplicate_groups = self._find_duplicates_internal(file_hashes)

        # 发送结果
        total_duplicates = sum(len(group) - 1 for group in duplicate_groups)  # 每组保留一个

        if duplicate_groups:
            self.duplicate_found_signal.emit(duplicate_groups)
            self.complete_signal.emit(True, f"检测完成！发现 {len(duplicate_groups)} 组重复图片，共 {total_duplicates} 个重复文件",
                                    total_files, total_duplicates)
        else:
            self.complete_signal.emit(True, "未发现重复图片", total_files, 0)

    def _run_compare_mode(self):
        """对比去重模式：将新文件夹与历史文件夹对比去重"""
        self.status_signal.emit("正在扫描新文件夹和历史文件夹...")

        # 支持的图片格式
        img_exts = ('.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.tif', '.webp')

        # 获取新文件夹的图片文件
        new_files = []
        for filename in os.listdir(self.folder_path):
            if not self.is_running:
                return

            file_path = os.path.join(self.folder_path, filename)
            if os.path.isfile(file_path) and filename.lower().endswith(img_exts):
                try:
                    file_size = os.path.getsize(file_path)
                    new_files.append((filename, file_path, file_size))
                except Exception:
                    continue

        # 获取历史文件夹的图片文件
        history_files = []
        if os.path.exists(self.history_folder):
            for filename in os.listdir(self.history_folder):
                if not self.is_running:
                    return

                file_path = os.path.join(self.history_folder, filename)
                if os.path.isfile(file_path) and filename.lower().endswith(img_exts):
                    try:
                        file_size = os.path.getsize(file_path)
                        history_files.append((filename, file_path, file_size))
                    except Exception:
                        continue

        total_new_files = len(new_files)
        total_history_files = len(history_files)

        if total_new_files == 0:
            self.complete_signal.emit(False, "新文件夹中未找到图片文件", 0, 0)
            return

        if total_history_files == 0:
            self.complete_signal.emit(False, "历史文件夹中未找到图片文件", 0, 0)
            return

        self.status_signal.emit(f"新文件夹: {total_new_files} 个文件，历史文件夹: {total_history_files} 个文件")

        # 计算历史文件夹的哈希值
        self.status_signal.emit("正在计算历史文件夹哈希值...")
        history_hashes = {}
        processed = 0

        for filename, filepath, filesize in history_files:
            if not self.is_running:
                return

            if self.comparison_method == "md5":
                hash_value = self.get_file_hash(filepath)
            else:
                hash_value = self.get_image_hash(filepath, "phash")

            if hash_value:
                history_hashes[filepath] = {
                    'filename': filename,
                    'hash': hash_value,
                    'size': filesize
                }

            processed += 1
            self.progress_signal.emit(processed, total_history_files + total_new_files)

        # 计算新文件夹的哈希值并与历史文件夹对比
        self.status_signal.emit("正在对比新文件夹与历史文件夹...")
        duplicate_files = []  # 在新文件夹中发现的重复文件

        for filename, filepath, filesize in new_files:
            if not self.is_running:
                return

            if self.comparison_method == "md5":
                hash_value = self.get_file_hash(filepath)
            else:
                hash_value = self.get_image_hash(filepath, "phash")

            if hash_value:
                # 与历史文件夹中的文件对比
                for history_info in history_hashes.values():
                    if self.comparison_method == "md5":
                        # MD5精确匹配
                        if hash_value == history_info['hash']:
                            duplicate_files.append({
                                'path': filepath,
                                'filename': filename,
                                'size': filesize,
                                'matched_history': history_info['filename']
                            })
                            break
                    else:
                        # 感知哈希相似度匹配
                        similarity = self.calculate_hash_similarity(hash_value, history_info['hash'])
                        if similarity >= self.similarity_threshold:
                            duplicate_files.append({
                                'path': filepath,
                                'filename': filename,
                                'size': filesize,
                                'matched_history': history_info['filename'],
                                'similarity': similarity
                            })
                            break

            processed += 1
            self.progress_signal.emit(processed, total_history_files + total_new_files)

            # 更新状态
            if processed % 10 == 0:
                self.status_signal.emit(f"已对比 {processed - total_history_files}/{total_new_files} 个新文件...")

        # 发送结果
        if duplicate_files:
            # 将重复文件转换为组格式以兼容现有界面
            duplicate_groups = [[file_info] for file_info in duplicate_files]
            self.duplicate_found_signal.emit(duplicate_groups)
            self.complete_signal.emit(True, f"对比完成！在新文件夹中发现 {len(duplicate_files)} 个与历史文件夹重复的文件",
                                    total_new_files, len(duplicate_files))
        else:
            self.complete_signal.emit(True, "未发现重复图片", total_new_files, 0)

    def _find_duplicates_internal(self, file_hashes):
        """在文件哈希中查找重复文件（内部去重）"""
        duplicate_groups = []
        processed_files = set()

        if self.comparison_method == "md5":
            # MD5精确匹配
            hash_groups = defaultdict(list)
            for filepath, info in file_hashes.items():
                hash_groups[info['hash']].append(filepath)

            for filepaths in hash_groups.values():
                if len(filepaths) > 1:
                    group = []
                    for fp in filepaths:
                        group.append({
                            'path': fp,
                            'filename': file_hashes[fp]['filename'],
                            'size': file_hashes[fp]['size']
                        })
                    duplicate_groups.append(group)
        else:
            # 感知哈希相似度匹配
            filepaths = list(file_hashes.keys())
            for i, filepath1 in enumerate(filepaths):
                if filepath1 in processed_files or not self.is_running:
                    continue

                group = [{'path': filepath1,
                         'filename': file_hashes[filepath1]['filename'],
                         'size': file_hashes[filepath1]['size']}]

                for filepath2 in filepaths[i+1:]:
                    if filepath2 in processed_files:
                        continue

                    similarity = self.calculate_hash_similarity(
                        file_hashes[filepath1]['hash'],
                        file_hashes[filepath2]['hash']
                    )

                    if similarity >= self.similarity_threshold:
                        group.append({
                            'path': filepath2,
                            'filename': file_hashes[filepath2]['filename'],
                            'size': file_hashes[filepath2]['size']
                        })
                        processed_files.add(filepath2)

                if len(group) > 1:
                    duplicate_groups.append(group)
                    processed_files.add(filepath1)

        return duplicate_groups

class DuplicateImageRemover(QWidget):
    """重复图删除功能界面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.detection_thread = None
        self.duplicate_groups = []
        self.init_ui()
        self.load_config()
        
    def init_ui(self):
        """初始化界面元素"""
        # 创建主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 添加说明标签
        description_label = QLabel("此功能使用高效算法检测和删除重复图片，支持精确匹配和相似度匹配")
        description_label.setWordWrap(True)
        main_layout.addWidget(description_label)
        
        # 工作模式选择
        mode_group = QGroupBox("工作模式")
        mode_layout = QVBoxLayout(mode_group)

        self.internal_mode_radio = QCheckBox("内部去重模式（在同一文件夹内查找重复图片）")
        self.internal_mode_radio.setChecked(True)
        self.internal_mode_radio.toggled.connect(self.on_mode_changed)
        mode_layout.addWidget(self.internal_mode_radio)

        self.compare_mode_radio = QCheckBox("对比去重模式（新文件夹与历史文件夹对比去重）")
        self.compare_mode_radio.toggled.connect(self.on_mode_changed)
        mode_layout.addWidget(self.compare_mode_radio)

        main_layout.addWidget(mode_group)

        # 新文件夹选择
        new_folder_layout = QHBoxLayout()
        self.new_folder_label = QLabel("选择新图片文件夹:")
        self.folder_input = QLineEdit()
        self.folder_input.setReadOnly(True)
        folder_button = QPushButton("浏览...")
        folder_button.clicked.connect(self.select_folder)

        new_folder_layout.addWidget(self.new_folder_label)
        new_folder_layout.addWidget(self.folder_input)
        new_folder_layout.addWidget(folder_button)
        main_layout.addLayout(new_folder_layout)

        # 历史文件夹选择（仅对比模式可用）
        history_folder_layout = QHBoxLayout()
        self.history_folder_label = QLabel("选择历史图片文件夹:")
        self.history_folder_input = QLineEdit()
        self.history_folder_input.setReadOnly(True)
        self.history_folder_button = QPushButton("浏览...")
        self.history_folder_button.clicked.connect(self.select_history_folder)

        history_folder_layout.addWidget(self.history_folder_label)
        history_folder_layout.addWidget(self.history_folder_input)
        history_folder_layout.addWidget(self.history_folder_button)
        main_layout.addLayout(history_folder_layout)
        self.history_folder_layout = history_folder_layout
        
        # 检测设置组
        settings_group = QGroupBox("检测设置")
        settings_layout = QVBoxLayout(settings_group)
        
        # 对比方法
        method_layout = QHBoxLayout()
        method_layout.addWidget(QLabel("对比方法:"))
        self.method_combo = QComboBox()
        self.method_combo.addItem("MD5哈希 (精确匹配)", "md5")

        if IMAGEHASH_AVAILABLE:
            self.method_combo.addItem("感知哈希 (相似度匹配)", "phash")
        else:
            # 如果imagehash不可用，添加一个禁用的选项作为提示
            self.method_combo.addItem("感知哈希 (需要安装imagehash库)", "phash_disabled")
            self.method_combo.model().item(1).setEnabled(False)

        self.method_combo.currentTextChanged.connect(self.on_method_changed)
        self.method_combo.currentTextChanged.connect(self.save_config)
        method_layout.addWidget(self.method_combo)
        method_layout.addStretch()
        settings_layout.addLayout(method_layout)
        
        # 相似度阈值（仅感知哈希可用）
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("相似度阈值:"))
        self.threshold_spinbox = QSpinBox()
        self.threshold_spinbox.setRange(80, 100)
        self.threshold_spinbox.setValue(95)
        self.threshold_spinbox.setSuffix("%")
        self.threshold_spinbox.valueChanged.connect(self.save_config)
        threshold_layout.addWidget(self.threshold_spinbox)
        threshold_layout.addStretch()
        settings_layout.addLayout(threshold_layout)
        self.threshold_layout = threshold_layout
        
        main_layout.addWidget(settings_group)
        
        # 选项设置
        options_layout = QVBoxLayout()
        self.auto_delete_checkbox = QCheckBox("自动删除重复文件（保留每组中的第一个）")
        self.auto_delete_checkbox.stateChanged.connect(self.save_config)
        options_layout.addWidget(self.auto_delete_checkbox)
        
        main_layout.addLayout(options_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%v/%m (%p%)")
        main_layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setWordWrap(True)
        main_layout.addWidget(self.status_label)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        self.detect_button = QPushButton("开始检测")
        self.detect_button.clicked.connect(self.start_detection)
        self.stop_button = QPushButton("停止检测")
        self.stop_button.clicked.connect(self.stop_detection)
        self.stop_button.setEnabled(False)
        
        button_layout.addWidget(self.detect_button)
        button_layout.addWidget(self.stop_button)
        main_layout.addLayout(button_layout)
        
        # 结果显示区域
        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(200)
        self.result_text.setReadOnly(True)
        main_layout.addWidget(self.result_text)
        
        # 删除按钮
        self.delete_button = QPushButton("删除选中的重复文件")
        self.delete_button.clicked.connect(self.delete_duplicates)
        self.delete_button.setEnabled(False)
        main_layout.addWidget(self.delete_button)
        
        # 添加弹性空间
        main_layout.addStretch()
        
        self.setLayout(main_layout)
        
        # 初始化界面状态
        self.on_method_changed()
        self.on_mode_changed()

    def on_mode_changed(self):
        """工作模式改变时的处理"""
        # 确保只有一个模式被选中
        if self.sender() == self.internal_mode_radio and self.internal_mode_radio.isChecked():
            self.compare_mode_radio.setChecked(False)
        elif self.sender() == self.compare_mode_radio and self.compare_mode_radio.isChecked():
            self.internal_mode_radio.setChecked(False)

        # 如果两个都没选中，默认选择内部去重
        if not self.internal_mode_radio.isChecked() and not self.compare_mode_radio.isChecked():
            self.internal_mode_radio.setChecked(True)

        # 根据模式显示/隐藏历史文件夹选择
        is_compare_mode = self.compare_mode_radio.isChecked()
        for i in range(self.history_folder_layout.count()):
            widget = self.history_folder_layout.itemAt(i).widget()
            if widget:
                widget.setVisible(is_compare_mode)

        # 更新标签文本
        if is_compare_mode:
            self.new_folder_label.setText("选择新图片文件夹:")
        else:
            self.new_folder_label.setText("选择图片文件夹:")

        self.save_config()

    def on_method_changed(self):
        """对比方法改变时的处理"""
        current_method = self.method_combo.currentData()

        # 如果选择了禁用的感知哈希选项，自动切换到MD5
        if current_method == "phash_disabled":
            self.method_combo.setCurrentIndex(0)  # 切换到MD5
            current_method = "md5"

        # 只有感知哈希方法才显示相似度阈值
        show_threshold = current_method == "phash" and IMAGEHASH_AVAILABLE
        for i in range(self.threshold_layout.count()):
            widget = self.threshold_layout.itemAt(i).widget()
            if widget:
                widget.setVisible(show_threshold)

    def load_config(self):
        """从主窗口加载配置"""
        if hasattr(self.parent, 'config') and self.parent.config:
            config = self.parent.config.get('duplicate_remover', {})

            # 设置上次使用的文件夹
            last_folder = config.get('last_folder', '')
            if last_folder and os.path.exists(last_folder):
                self.folder_input.setText(last_folder)

            # 设置上次使用的历史文件夹
            last_history_folder = config.get('last_history_folder', '')
            if last_history_folder and os.path.exists(last_history_folder):
                self.history_folder_input.setText(last_history_folder)

            # 设置工作模式
            comparison_mode = config.get('comparison_mode', 'internal')
            if comparison_mode == 'compare':
                self.compare_mode_radio.setChecked(True)
                self.internal_mode_radio.setChecked(False)
            else:
                self.internal_mode_radio.setChecked(True)
                self.compare_mode_radio.setChecked(False)

            # 设置对比方法
            method = config.get('comparison_method', 'md5')
            # 如果配置中是phash但imagehash不可用，则使用md5
            if method == "phash" and not IMAGEHASH_AVAILABLE:
                method = "md5"

            for i in range(self.method_combo.count()):
                if self.method_combo.itemData(i) == method:
                    self.method_combo.setCurrentIndex(i)
                    break

            # 设置相似度阈值
            threshold = config.get('similarity_threshold', 95)
            self.threshold_spinbox.setValue(threshold)

            # 设置自动删除
            auto_delete = config.get('auto_delete', False)
            self.auto_delete_checkbox.setChecked(auto_delete)

    def save_config(self):
        """保存配置到主窗口"""
        if hasattr(self.parent, 'config'):
            if 'duplicate_remover' not in self.parent.config:
                self.parent.config['duplicate_remover'] = {}

            # 保存当前设置
            self.parent.config['duplicate_remover']['last_folder'] = self.folder_input.text()
            self.parent.config['duplicate_remover']['last_history_folder'] = self.history_folder_input.text()

            # 保存工作模式
            if self.compare_mode_radio.isChecked():
                self.parent.config['duplicate_remover']['comparison_mode'] = 'compare'
            else:
                self.parent.config['duplicate_remover']['comparison_mode'] = 'internal'

            current_method = self.method_combo.currentData()
            if current_method:
                self.parent.config['duplicate_remover']['comparison_method'] = current_method

            self.parent.config['duplicate_remover']['similarity_threshold'] = self.threshold_spinbox.value()
            self.parent.config['duplicate_remover']['auto_delete'] = self.auto_delete_checkbox.isChecked()

    def select_folder(self):
        """选择新图片文件夹"""
        start_dir = self.folder_input.text() if self.folder_input.text() else ""
        folder_path = QFileDialog.getExistingDirectory(self, "选择新图片文件夹", start_dir)
        if folder_path:
            self.folder_input.setText(folder_path)
            logging.info(f"已选择新图片文件夹: {folder_path}")
            self.save_config()

    def select_history_folder(self):
        """选择历史图片文件夹"""
        start_dir = self.history_folder_input.text() if self.history_folder_input.text() else ""
        folder_path = QFileDialog.getExistingDirectory(self, "选择历史图片文件夹", start_dir)
        if folder_path:
            self.history_folder_input.setText(folder_path)
            logging.info(f"已选择历史图片文件夹: {folder_path}")
            self.save_config()

    def start_detection(self):
        """开始检测重复图片"""
        folder_path = self.folder_input.text()
        if not folder_path or not os.path.exists(folder_path):
            QMessageBox.warning(self, "警告", "请选择有效的新图片文件夹")
            return

        # 检查工作模式
        is_compare_mode = self.compare_mode_radio.isChecked()
        history_folder = None

        if is_compare_mode:
            history_folder = self.history_folder_input.text()
            if not history_folder or not os.path.exists(history_folder):
                QMessageBox.warning(self, "警告", "对比去重模式需要选择有效的历史图片文件夹")
                return

        # 获取设置
        method = self.method_combo.currentData() or "md5"
        # 如果选择了禁用的感知哈希，强制使用MD5
        if method == "phash_disabled" or (method == "phash" and not IMAGEHASH_AVAILABLE):
            method = "md5"

        threshold = self.threshold_spinbox.value()
        comparison_mode = "compare" if is_compare_mode else "internal"

        # 保存配置
        self.save_config()

        # 重置界面
        self.result_text.clear()
        self.duplicate_groups = []
        self.progress_bar.setValue(0)
        self.status_label.setText("准备开始检测...")

        # 更新按钮状态
        self.detect_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.delete_button.setEnabled(False)

        # 创建并启动检测线程
        self.detection_thread = DuplicateDetectionThread(
            folder_path, method, threshold, history_folder, comparison_mode
        )
        self.detection_thread.progress_signal.connect(self.update_progress)
        self.detection_thread.status_signal.connect(self.update_status)
        self.detection_thread.duplicate_found_signal.connect(self.on_duplicates_found)
        self.detection_thread.complete_signal.connect(self.on_detection_complete)
        self.detection_thread.start()

        if is_compare_mode:
            logging.info(f"开始对比去重: 新文件夹={folder_path}, 历史文件夹={history_folder}, 方法={method}, 阈值={threshold}%")
        else:
            logging.info(f"开始内部去重: 文件夹={folder_path}, 方法={method}, 阈值={threshold}%")

    def stop_detection(self):
        """停止检测"""
        if self.detection_thread and self.detection_thread.isRunning():
            self.detection_thread.stop()
            self.detection_thread.wait()
            logging.info("重复图片检测已停止")

        self.detect_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("检测已停止")

    def update_progress(self, current, total):
        """更新进度条"""
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)

    def update_status(self, message):
        """更新状态标签"""
        self.status_label.setText(message)
        logging.info(message)

    def on_duplicates_found(self, duplicate_groups):
        """处理发现的重复图片"""
        self.duplicate_groups = duplicate_groups

        # 检查是否为对比去重模式
        is_compare_mode = self.compare_mode_radio.isChecked()

        if is_compare_mode:
            # 对比去重模式的结果显示
            result_text = f"在新文件夹中发现 {len(duplicate_groups)} 个与历史文件夹重复的文件:\n\n"

            for i, group in enumerate(duplicate_groups, 1):
                file_info = group[0]  # 对比模式每组只有一个文件
                filename = file_info['filename']
                size = file_info['size']
                size_mb = size / (1024 * 1024)
                matched_history = file_info.get('matched_history', '未知')

                result_text += f"{i}. {filename} ({size_mb:.2f} MB)\n"
                result_text += f"   与历史文件匹配: {matched_history}\n"

                # 如果有相似度信息，显示相似度
                if 'similarity' in file_info:
                    similarity = file_info['similarity']
                    result_text += f"   相似度: {similarity:.1f}%\n"

                result_text += "\n"
        else:
            # 内部去重模式的结果显示
            result_text = f"发现 {len(duplicate_groups)} 组重复图片:\n\n"

            for i, group in enumerate(duplicate_groups, 1):
                result_text += f"第 {i} 组 ({len(group)} 个文件):\n"
                for j, file_info in enumerate(group):
                    filename = file_info['filename']
                    size = file_info['size']
                    size_mb = size / (1024 * 1024)
                    marker = " [保留]" if j == 0 else " [删除]"
                    result_text += f"  • {filename} ({size_mb:.2f} MB){marker}\n"
                result_text += "\n"

        self.result_text.setPlainText(result_text)

        # 如果设置了自动删除，则自动执行删除
        if self.auto_delete_checkbox.isChecked():
            self.delete_duplicates()

    def on_detection_complete(self, success, message, _total_files, duplicate_count):
        """检测完成处理"""
        self.detect_button.setEnabled(True)
        self.stop_button.setEnabled(False)

        if success and duplicate_count > 0:
            self.delete_button.setEnabled(True)

        self.status_label.setText(message)

        if success:
            if duplicate_count > 0:
                QMessageBox.information(self, "检测完成", message)
            else:
                QMessageBox.information(self, "检测完成", "未发现重复图片")
        else:
            QMessageBox.critical(self, "检测失败", message)

    def delete_duplicates(self):
        """删除重复文件"""
        if not self.duplicate_groups:
            QMessageBox.warning(self, "警告", "没有发现重复文件")
            return

        # 检查是否为对比去重模式
        is_compare_mode = self.compare_mode_radio.isChecked()

        if is_compare_mode:
            # 对比去重模式：删除所有在新文件夹中的重复文件
            total_to_delete = len(self.duplicate_groups)

            if not self.auto_delete_checkbox.isChecked():
                reply = QMessageBox.question(
                    self, "确认删除",
                    f"即将删除新文件夹中 {total_to_delete} 个与历史文件夹重复的文件。\n\n"
                    "删除的文件无法恢复，确定要继续吗？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply != QMessageBox.Yes:
                    return

            # 执行删除
            deleted_count = 0
            failed_count = 0

            for group in self.duplicate_groups:
                file_info = group[0]  # 对比模式每组只有一个文件
                try:
                    os.remove(file_info['path'])
                    deleted_count += 1
                    logging.info(f"已删除重复文件: {file_info['filename']}")
                except Exception as e:
                    failed_count += 1
                    logging.error(f"删除文件失败 {file_info['filename']}: {str(e)}")
        else:
            # 内部去重模式：每组保留第一个文件
            total_to_delete = sum(len(group) - 1 for group in self.duplicate_groups)

            if not self.auto_delete_checkbox.isChecked():
                reply = QMessageBox.question(
                    self, "确认删除",
                    f"即将删除 {total_to_delete} 个重复文件，每组保留第一个文件。\n\n"
                    "删除的文件无法恢复，确定要继续吗？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply != QMessageBox.Yes:
                    return

            # 执行删除
            deleted_count = 0
            failed_count = 0

            for group in self.duplicate_groups:
                # 跳过第一个文件（保留）
                for file_info in group[1:]:
                    try:
                        os.remove(file_info['path'])
                        deleted_count += 1
                        logging.info(f"已删除重复文件: {file_info['filename']}")
                    except Exception as e:
                        failed_count += 1
                        logging.error(f"删除文件失败 {file_info['filename']}: {str(e)}")

        # 显示结果
        result_msg = f"删除完成！\n成功删除: {deleted_count} 个文件"
        if failed_count > 0:
            result_msg += f"\n删除失败: {failed_count} 个文件"

        QMessageBox.information(self, "删除完成", result_msg)

        # 清空结果
        self.duplicate_groups = []
        self.result_text.clear()
        self.delete_button.setEnabled(False)
        self.status_label.setText("删除完成")

    def closeEvent(self, event):
        """关闭窗口时停止线程"""
        if self.detection_thread and self.detection_thread.isRunning():
            self.detection_thread.stop()
            self.detection_thread.wait()
        event.accept()

# 为了兼容性，保留原来的类名
重复图删除 = DuplicateImageRemover
重复图检测线程 = DuplicateDetectionThread
